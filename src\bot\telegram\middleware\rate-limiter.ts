// Advanced rate limiting and quota management for production scale

import { TBotContext } from "../core/types";
import { RepositoryFactory } from "@/repositories";
import { RATE_LIMITS, USER_QUOTAS } from "../constants/config";

export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  blockDurationMs: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (ctx: TBotContext) => string;
}

export interface QuotaConfig {
  daily: number;
  hourly: number;
  monthly: number;
  resetHour?: number; // Hour of day to reset daily quotas (0-23)
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  retryAfter?: number;
}

export class ProductionRateLimiter {
  private repositoryFactory: RepositoryFactory;
  private configs: Map<string, RateLimitConfig>;
  private quotaConfigs: Map<string, QuotaConfig>;

  constructor(repositoryFactory: RepositoryFactory) {
    this.repositoryFactory = repositoryFactory;
    this.configs = new Map();
    this.quotaConfigs = new Map();
    this.setupDefaultConfigs();
  }

  /**
   * Setup default rate limiting configurations
   */
  private setupDefaultConfigs(): void {
    // General API rate limiting
    this.configs.set("general", {
      windowMs: RATE_LIMITS.GENERAL.WINDOW_MS,
      maxRequests: RATE_LIMITS.GENERAL.MAX_REQUESTS,
      blockDurationMs: RATE_LIMITS.GENERAL.BLOCK_DURATION_MS,
      keyGenerator: (ctx) => `general:${ctx.from?.id}`
    });

    // AI API rate limiting (more restrictive)
    this.configs.set("ai", {
      windowMs: RATE_LIMITS.AI.WINDOW_MS,
      maxRequests: RATE_LIMITS.AI.MAX_REQUESTS,
      blockDurationMs: RATE_LIMITS.AI.BLOCK_DURATION_MS,
      keyGenerator: (ctx) => `ai:${ctx.from?.id}`
    });

    // Command rate limiting
    this.configs.set("commands", {
      windowMs: RATE_LIMITS.COMMANDS.WINDOW_MS,
      maxRequests: RATE_LIMITS.COMMANDS.MAX_REQUESTS,
      blockDurationMs: RATE_LIMITS.COMMANDS.BLOCK_DURATION_MS,
      keyGenerator: (ctx) => `cmd:${ctx.from?.id}`
    });

    // Callback query rate limiting
    this.configs.set("callbacks", {
      windowMs: RATE_LIMITS.CALLBACKS.WINDOW_MS,
      maxRequests: RATE_LIMITS.CALLBACKS.MAX_REQUESTS,
      blockDurationMs: RATE_LIMITS.CALLBACKS.BLOCK_DURATION_MS,
      keyGenerator: (ctx) => `cb:${ctx.from?.id}`
    });

    // Setup quota configurations
    this.quotaConfigs.set("free", {
      daily: USER_QUOTAS.FREE.DAILY_ACTIONS,
      hourly: 10,
      monthly: 1000,
      resetHour: 0
    });

    this.quotaConfigs.set("paid", {
      daily: USER_QUOTAS.PAID.DAILY_ACTIONS,
      hourly: 100,
      monthly: 30000,
      resetHour: 0
    });

    this.quotaConfigs.set("admin", {
      daily: USER_QUOTAS.ADMIN.DAILY_ACTIONS,
      hourly: -1,
      monthly: -1,
      resetHour: 0
    });
  }

  /**
   * Check rate limit for a specific context and type
   */
  async checkRateLimit(ctx: TBotContext, type: string = "general"): Promise<RateLimitResult> {
    const config = this.configs.get(type);
    if (!config) {
      throw new Error(`Rate limit config not found for type: ${type}`);
    }

    const key = config.keyGenerator ? config.keyGenerator(ctx) : `${type}:${ctx.from?.id}`;
    const now = new Date();

    try {
      // Get current rate limit data from storage
      const rateLimitData = await this.getRateLimitData(key);

      // Check if user is currently blocked
      if (rateLimitData.isBlocked && rateLimitData.blockUntil && rateLimitData.blockUntil > now) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: rateLimitData.blockUntil,
          retryAfter: Math.ceil((rateLimitData.blockUntil.getTime() - now.getTime()) / 1000)
        };
      }

      // Reset block if expired
      if (rateLimitData.isBlocked && rateLimitData.blockUntil && rateLimitData.blockUntil <= now) {
        rateLimitData.isBlocked = false;
        rateLimitData.blockUntil = undefined;
        rateLimitData.requestCount = 0;
        rateLimitData.windowStart = now;
      }

      // Check if we need to reset the window
      if (now.getTime() - rateLimitData.windowStart.getTime() > config.windowMs) {
        rateLimitData.requestCount = 0;
        rateLimitData.windowStart = now;
      }

      // Check rate limit
      if (rateLimitData.requestCount >= config.maxRequests) {
        // Block the user
        rateLimitData.isBlocked = true;
        rateLimitData.blockUntil = new Date(now.getTime() + config.blockDurationMs);
        await this.saveRateLimitData(key, rateLimitData);

        return {
          allowed: false,
          remaining: 0,
          resetTime: rateLimitData.blockUntil,
          retryAfter: Math.ceil(config.blockDurationMs / 1000)
        };
      }

      // Increment request count
      rateLimitData.requestCount++;
      rateLimitData.lastRequest = now;
      await this.saveRateLimitData(key, rateLimitData);

      const remaining = config.maxRequests - rateLimitData.requestCount;
      const resetTime = new Date(rateLimitData.windowStart.getTime() + config.windowMs);

      return {
        allowed: true,
        remaining,
        resetTime
      };
    } catch (error) {
      console.error("Rate limit check error:", error);
      // Fail open - allow the request if we can't check rate limits
      return {
        allowed: true,
        remaining: config.maxRequests,
        resetTime: new Date(now.getTime() + config.windowMs)
      };
    }
  }

  /**
   * Check quota limits for user tier
   */
  async checkQuota(ctx: TBotContext, quotaType: "daily" | "hourly" | "monthly"): Promise<RateLimitResult> {
    const userTier = ctx.session?.user?.tier.type || "free";
    const quotaConfig = this.quotaConfigs.get(userTier);

    if (!quotaConfig) {
      throw new Error(`Quota config not found for tier: ${userTier}`);
    }

    const limit = quotaConfig[quotaType];

    // Unlimited quota (admin)
    if (limit === -1) {
      return {
        allowed: true,
        remaining: -1,
        resetTime: this.getNextResetTime(quotaType, quotaConfig.resetHour)
      };
    }

    const userId = ctx.from?.id.toString();
    if (!userId) {
      return { allowed: false, remaining: 0, resetTime: new Date() };
    }

    try {
      const quotaData = await this.getQuotaData(userId, quotaType);
      const now = new Date();

      // Check if quota needs reset
      if (quotaData.resetTime <= now) {
        quotaData.used = 0;
        quotaData.resetTime = this.getNextResetTime(quotaType, quotaConfig.resetHour);
      }

      const remaining = limit - quotaData.used;

      if (remaining <= 0) {
        return {
          allowed: false,
          remaining: 0,
          resetTime: quotaData.resetTime
        };
      }

      return {
        allowed: true,
        remaining,
        resetTime: quotaData.resetTime
      };
    } catch (error) {
      console.error("Quota check error:", error);
      // Fail open for quota checks
      return {
        allowed: true,
        remaining: limit,
        resetTime: this.getNextResetTime(quotaType, quotaConfig.resetHour)
      };
    }
  }

  /**
   * Consume quota for a user
   */
  async consumeQuota(ctx: TBotContext, quotaType: "daily" | "hourly" | "monthly", amount: number = 1): Promise<boolean> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return false;
    }

    const userTier = ctx.session?.user?.tier.type || "free";
    const quotaConfig = this.quotaConfigs.get(userTier);

    if (!quotaConfig) {
      return false;
    }

    const limit = quotaConfig[quotaType];

    // Unlimited quota (admin)
    if (limit === -1) {
      return true;
    }

    try {
      const quotaData = await this.getQuotaData(userId, quotaType);
      const now = new Date();

      // Check if quota needs reset
      if (quotaData.resetTime <= now) {
        quotaData.used = 0;
        quotaData.resetTime = this.getNextResetTime(quotaType, quotaConfig.resetHour);
      }

      if (quotaData.used + amount <= limit) {
        quotaData.used += amount;
        await this.saveQuotaData(userId, quotaType, quotaData);
        return true;
      }

      return false;
    } catch (error) {
      console.error("Quota consumption error:", error);
      return false;
    }
  }

  /**
   * Get rate limit data from storage
   */
  private async getRateLimitData(key: string): Promise<any> {
    // Implementation would use D1 database or Redis
    // For now, return default data
    return {
      requestCount: 0,
      windowStart: new Date(),
      lastRequest: new Date(),
      isBlocked: false,
      blockUntil: undefined
    };
  }

  /**
   * Save rate limit data to storage
   */
  private async saveRateLimitData(key: string, data: any): Promise<void> {
    // Implementation would save to D1 database or Redis
    console.log(`Saving rate limit data for ${key}:`, data);
  }

  /**
   * Get quota data from storage
   */
  private async getQuotaData(userId: string, quotaType: string): Promise<{ used: number; resetTime: Date }> {
    // Implementation would use D1 database
    // For now, return default data
    return {
      used: 0,
      resetTime: this.getNextResetTime(quotaType as "daily" | "hourly" | "monthly")
    };
  }

  /**
   * Save quota data to storage
   */
  private async saveQuotaData(userId: string, quotaType: string, data: { used: number; resetTime: Date }): Promise<void> {
    // Implementation would save to D1 database
    console.log(`Saving quota data for ${userId}:${quotaType}:`, data);
  }

  /**
   * Get next reset time for quota type
   */
  private getNextResetTime(quotaType: "daily" | "hourly" | "monthly", resetHour: number = 0): Date {
    const now = new Date();
    const resetTime = new Date();

    switch (quotaType) {
    case "hourly":
      resetTime.setHours(now.getHours() + 1, 0, 0, 0);
      break;
    case "daily":
      resetTime.setDate(now.getDate() + 1);
      resetTime.setHours(resetHour, 0, 0, 0);
      break;
    case "monthly":
      resetTime.setMonth(now.getMonth() + 1, 1);
      resetTime.setHours(resetHour, 0, 0, 0);
      break;
    }

    return resetTime;
  }

  /**
   * Get rate limit status for monitoring
   */
  async getRateLimitStatus(userId: string): Promise<{
    general: RateLimitResult;
    ai: RateLimitResult;
    commands: RateLimitResult;
    callbacks: RateLimitResult;
  }> {
    // This would be used for monitoring and admin panels
    const mockResult: RateLimitResult = {
      allowed: true,
      remaining: 10,
      resetTime: new Date(Date.now() + 60000)
    };

    return {
      general: mockResult,
      ai: mockResult,
      commands: mockResult,
      callbacks: mockResult
    };
  }

  /**
   * Reset rate limits for a user (admin function)
   */
  async resetRateLimits(userId: string): Promise<void> {
    // Implementation would clear all rate limit data for the user
    console.log(`Resetting rate limits for user: ${userId}`);
  }

  /**
   * Block/unblock a user (admin function)
   */
  async setUserBlock(userId: string, blocked: boolean, reason?: string): Promise<void> {
    // Implementation would set user block status
    console.log(`Setting user ${userId} block status to ${blocked}, reason: ${reason}`);
  }
}
