// Configuration constants for Telegram bot

export const RATE_LIMITS = {
  GENERAL: {
    WINDOW_MS: 60 * 1000, // 1 minute
    MAX_REQUESTS: 30,
    BLOCK_DURATION_MS: 15 * 60 * 1000 // 15 minutes
  },
  AI: {
    WINDOW_MS: 60 * 60 * 1000, // 1 hour
    MAX_REQUESTS: 10,
    BLOCK_DURATION_MS: 60 * 60 * 1000 // 1 hour
  },
  COMMANDS: {
    WINDOW_MS: 60 * 1000, // 1 minute
    MAX_REQUESTS: 20,
    BLOCK_DURATION_MS: 5 * 60 * 1000 // 5 minutes
  },
  CALLBACKS: {
    WINDOW_MS: 10 * 1000, // 10 seconds
    MAX_REQUESTS: 10,
    BLOCK_DURATION_MS: 30 * 1000 // 30 seconds
  }
};

export const USER_QUOTAS = {
  FREE: {
    TASKS: 10,
    AI_REQUESTS: 3,
    BIRTHDAYS: 5,
    NOTIFICATIONS: 20,
    DAILY_ACTIONS: 50,
    EXPORTS: 1
  },
  PAID: {
    TASKS: -1, // Unlimited
    AI_REQUESTS: 100,
    BIRTHDAYS: -1,
    NOTIFICATIONS: -1,
    DAILY_ACTIONS: 1000,
    EXPORTS: 10
  },
  ADMIN: {
    TASKS: -1,
    AI_REQUESTS: -1,
    BIRTHDAYS: -1,
    NOTIFICATIONS: -1,
    DAILY_ACTIONS: -1,
    EXPORTS: -1
  }
};

export const SUBSCRIPTION_PLANS = {
  PREMIUM_MONTHLY: {
    ID: "premium_monthly",
    NAME: "Premium Monthly",
    PRICE: 9.99,
    CURRENCY: "USD",
    INTERVAL: "monthly" as const,
    TRIAL_DAYS: 7
  },
  PREMIUM_YEARLY: {
    ID: "premium_yearly",
    NAME: "Premium Yearly",
    PRICE: 99.99,
    CURRENCY: "USD",
    INTERVAL: "yearly" as const,
    TRIAL_DAYS: 14,
    POPULAR: true
  }
};

export const FEATURES = {
  FREE: [
    "basic_tasks",
    "basic_birthdays",
    "basic_ai",
    "basic_notifications",
    "standard_themes"
  ],
  PAID: [
    "unlimited_tasks",
    "unlimited_birthdays",
    "enhanced_ai",
    "advanced_notifications",
    "export_data",
    "priority_support",
    "custom_themes",
    "task_templates",
    "bulk_operations"
  ],
  ADMIN: [
    "unlimited_tasks",
    "unlimited_birthdays",
    "unlimited_ai",
    "advanced_analytics",
    "user_management",
    "broadcast_messages",
    "export_data",
    "priority_support",
    "custom_themes",
    "api_access"
  ]
};

export const ANALYTICS_CONFIG = {
  FLUSH_INTERVAL: 30000, // 30 seconds
  MAX_BUFFER_SIZE: 1000,
  RETENTION_DAYS: 90
};

export const SESSION_CONFIG = {
  TIMEOUT_MINUTES: 30,
  CLEANUP_INTERVAL: 60 * 60 * 1000, // 1 hour
  MAX_SESSIONS_PER_USER: 5
};


export const SCENES = {
  TASK_CREATION: "task-creation-wizard",
  BIRTHDAY_CREATION: "birthday-creation-wizard",
  SETTINGS: "settings-wizard",
  SUBSCRIPTION: "subscription-wizard",
  SEARCH: "search-wizard"
};
