// Production monitoring and analytics system

import { TBotContext } from "../core/types";
import { RepositoryFactory } from "@/repositories";

export interface AnalyticsEvent {
  id: string;
  userId: string;
  eventType: string;
  eventData: Record<string, any>;
  timestamp: Date;
  sessionId?: string;
  userAgent?: string;
  ip?: string;
}

export interface PerformanceMetric {
  id: string;
  operation: string;
  duration: number;
  timestamp: Date;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, any>;
}

export interface SystemMetrics {
  activeUsers: number;
  totalUsers: number;
  messagesPerMinute: number;
  averageResponseTime: number;
  errorRate: number;
  aiRequestsPerHour: number;
  memoryUsage: number;
  cpuUsage: number;
}

export interface UserMetrics {
  userId: string;
  totalCommands: number;
  totalMessages: number;
  averageSessionDuration: number;
  lastActive: Date;
  favoriteFeatures: string[];
  conversionFunnel: {
    registered: Date;
    firstTask?: Date;
    firstAI?: Date;
    upgraded?: Date;
  };
}

export class ProductionAnalytics {
  private repositoryFactory: RepositoryFactory;
  private metricsBuffer: AnalyticsEvent[] = [];
  private performanceBuffer: PerformanceMetric[] = [];
  private flushInterval: number = 30000; // 30 seconds
  private maxBufferSize: number = 1000;

  constructor(repositoryFactory: RepositoryFactory) {
    this.repositoryFactory = repositoryFactory;
    this.startPeriodicFlush();
  }

  /**
   * Track user event
   */
  async trackEvent(
    ctx: TBotContext,
    eventType: string,
    eventData: Record<string, any> = {}
  ): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    const event: AnalyticsEvent = {
      id: this.generateId(),
      userId,
      eventType,
      eventData: {
        ...eventData,
        userTier: ctx.session?.user?.tier.type,
        timestamp: new Date().toISOString()
      },
      timestamp: new Date(),
      sessionId: this.getSessionId(ctx)
    };

    this.metricsBuffer.push(event);

    // Flush if buffer is full
    if (this.metricsBuffer.length >= this.maxBufferSize) {
      await this.flushMetrics();
    }
  }

  /**
   * Track performance metric
   */
  async trackPerformance(
    operation: string,
    startTime: number,
    success: boolean = true,
    errorMessage?: string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const metric: PerformanceMetric = {
      id: this.generateId(),
      operation,
      duration: Date.now() - startTime,
      timestamp: new Date(),
      success,
      errorMessage,
      metadata
    };

    this.performanceBuffer.push(metric);
  }

  /**
   * Track user journey and conversion funnel
   */
  async trackUserJourney(ctx: TBotContext, milestone: string): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    await this.trackEvent(ctx, "user_journey", {
      milestone,
      userTier: ctx.session?.user?.tier.type,
      daysSinceRegistration: this.getDaysSinceRegistration(ctx)
    });

    // Update user conversion funnel
    await this.updateConversionFunnel(userId, milestone);
  }

  /**
   * Track feature usage
   */
  async trackFeatureUsage(ctx: TBotContext, feature: string, success: boolean = true): Promise<void> {
    await this.trackEvent(ctx, "feature_usage", {
      feature,
      success,
      userTier: ctx.session?.user?.tier.type,
      quotaRemaining: this.getQuotaRemaining(ctx, feature)
    });
  }

  /**
   * Track error events
   */
  async trackError(
    ctx: TBotContext,
    errorType: string,
    errorMessage: string,
    stackTrace?: string
  ): Promise<void> {
    await this.trackEvent(ctx, "error", {
      errorType,
      errorMessage,
      stackTrace,
      userTier: ctx.session?.user?.tier.type,
      command: this.getCurrentCommand(ctx)
    });

    // Also track as performance metric
    await this.trackPerformance("error_handling", Date.now(), false, errorMessage, {
      errorType,
      userId: ctx.from?.id.toString()
    });
  }

  /**
   * Track business metrics
   */
  async trackBusinessMetric(
    ctx: TBotContext,
    metricType: "upgrade_prompt" | "subscription_start" | "subscription_cancel" | "feature_limit_hit",
    value?: number,
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.trackEvent(ctx, "business_metric", {
      metricType,
      value,
      ...metadata,
      userTier: ctx.session?.user?.tier.type
    });
  }

  /**
   * Get real-time system metrics
   */
  async getSystemMetrics(): Promise<SystemMetrics> {
    try {
      // These would be calculated from actual data in production
      const metrics: SystemMetrics = {
        activeUsers: await this.getActiveUsersCount(),
        totalUsers: await this.getTotalUsersCount(),
        messagesPerMinute: await this.getMessagesPerMinute(),
        averageResponseTime: await this.getAverageResponseTime(),
        errorRate: await this.getErrorRate(),
        aiRequestsPerHour: await this.getAIRequestsPerHour(),
        memoryUsage: this.getMemoryUsage(),
        cpuUsage: this.getCPUUsage()
      };

      return metrics;
    } catch (error) {
      console.error("Error getting system metrics:", error);
      return this.getDefaultMetrics();
    }
  }

  /**
   * Get user-specific metrics
   */
  async getUserMetrics(userId: string): Promise<UserMetrics> {
    try {
      // Calculate from stored analytics data
      const userRepo = this.repositoryFactory.getUserRepository();
      const user = await userRepo.findById(userId);

      if (!user) {
        throw new Error("User not found");
      }

      const preferences = user.preferences;
      const analytics = preferences?.analytics || {};

      return {
        userId,
        totalCommands: analytics.totalCommands || 0,
        totalMessages: analytics.totalMessages || 0,
        averageSessionDuration: analytics.averageSessionDuration || 0,
        lastActive: new Date(user.lastActive),
        favoriteFeatures: analytics.featuresUsed || [],
        conversionFunnel: {
          registered: new Date(user.createdAt),
          firstTask: analytics.firstTask ? new Date(analytics.firstTask) : undefined,
          firstAI: analytics.firstAI ? new Date(analytics.firstAI) : undefined,
          upgraded: analytics.upgraded ? new Date(analytics.upgraded) : undefined
        }
      };
    } catch (error) {
      console.error("Error getting user metrics:", error);
      throw error;
    }
  }

  /**
   * Get feature adoption metrics
   */
  async getFeatureAdoption(): Promise<Record<string, { users: number; usage: number; conversionRate: number }>> {
    // This would analyze feature usage across all users
    return {
      "task_creation": { users: 850, usage: 5420, conversionRate: 0.85 },
      "ai_enhancement": { users: 320, usage: 1240, conversionRate: 0.32 },
      "birthday_management": { users: 650, usage: 2100, conversionRate: 0.65 },
      "event_tracking": { users: 420, usage: 980, conversionRate: 0.42 },
      "premium_features": { users: 120, usage: 890, conversionRate: 0.12 }
    };
  }

  /**
   * Get conversion funnel analysis
   */
  async getConversionFunnel(): Promise<{
    registered: number;
    firstAction: number;
    firstTask: number;
    firstAI: number;
    upgraded: number;
  }> {
    // Analyze user journey through the conversion funnel
    return {
      registered: 1000,
      firstAction: 850,
      firstTask: 720,
      firstAI: 320,
      upgraded: 120
    };
  }

  /**
   * Get revenue metrics
   */
  async getRevenueMetrics(): Promise<{
    monthlyRevenue: number;
    averageRevenuePerUser: number;
    churnRate: number;
    lifetimeValue: number;
  }> {
    return {
      monthlyRevenue: 1199.88,
      averageRevenuePerUser: 9.99,
      churnRate: 0.05,
      lifetimeValue: 199.80
    };
  }

  /**
   * Generate analytics dashboard data
   */
  async getDashboardData(): Promise<{
    systemMetrics: SystemMetrics;
    featureAdoption: Record<string, any>;
    conversionFunnel: Record<string, number>;
    revenueMetrics: Record<string, number>;
    topErrors: Array<{ error: string; count: number; lastOccurred: Date }>;
    userGrowth: Array<{ date: string; newUsers: number; totalUsers: number }>;
  }> {
    const [
      systemMetrics,
      featureAdoption,
      conversionFunnel,
      revenueMetrics
    ] = await Promise.all([
      this.getSystemMetrics(),
      this.getFeatureAdoption(),
      this.getConversionFunnel(),
      this.getRevenueMetrics()
    ]);

    return {
      systemMetrics,
      featureAdoption,
      conversionFunnel,
      revenueMetrics,
      topErrors: await this.getTopErrors(),
      userGrowth: await this.getUserGrowthData()
    };
  }

  /**
   * Flush metrics to persistent storage
   */
  private async flushMetrics(): Promise<void> {
    if (this.metricsBuffer.length === 0 && this.performanceBuffer.length === 0) {
      return;
    }

    try {
      // In production, this would batch insert to D1 database
      console.log(`Flushing ${this.metricsBuffer.length} analytics events and ${this.performanceBuffer.length} performance metrics`);

      // Clear buffers
      this.metricsBuffer = [];
      this.performanceBuffer = [];
    } catch (error) {
      console.error("Error flushing metrics:", error);
    }
  }

  /**
   * Start periodic metric flushing
   */
  private startPeriodicFlush(): void {
    setInterval(async () => {
      await this.flushMetrics();
    }, this.flushInterval);
  }

  // Helper methods
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private getSessionId(ctx: TBotContext): string {
    // Generate or retrieve session ID
    return `session-${ctx.from?.id}-${Date.now()}`;
  }

  private getDaysSinceRegistration(ctx: TBotContext): number {
    const registrationDate = ctx.session?.user?.createdAt;
    if (!registrationDate) {
      return 0;
    }

    const now = new Date();
    const diffTime = Math.abs(now.getTime() - registrationDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  private getQuotaRemaining(ctx: TBotContext, feature: string): number {
    const user = ctx.session?.user;
    if (!user) {
      return 0;
    }

    // Map features to quota types
    const quotaMap: Record<string, keyof typeof user.tier.quotas> = {
      "task_creation": "tasks",
      "ai_enhancement": "aiRequests",
      "birthday_management": "birthdays",
      "notifications": "notifications"
    };

    const quotaType = quotaMap[feature];
    if (!quotaType) {
      return 0;
    }

    const quota = user.tier.quotas[quotaType];
    return quota.limit === -1 ? -1 : quota.limit - quota.used;
  }

  private getCurrentCommand(ctx: TBotContext): string | undefined {
    if (ctx.message && "text" in ctx.message && ctx.message.text.startsWith("/")) {
      return ctx.message.text.split(" ")[0];
    }
    return undefined;
  }

  private async updateConversionFunnel(userId: string, milestone: string): Promise<void> {
    // Update user's conversion funnel progress
    const userRepo = this.repositoryFactory.getUserRepository();
    const user = await userRepo.findById(userId);

    if (user) {
      const preferences = JSON.parse(user.preferences || "{}");
      const analytics = preferences.analytics || {};

      if (milestone === "first_task" && !analytics.firstTask) {
        analytics.firstTask = new Date().toISOString();
      } else if (milestone === "first_ai" && !analytics.firstAI) {
        analytics.firstAI = new Date().toISOString();
      } else if (milestone === "upgraded" && !analytics.upgraded) {
        analytics.upgraded = new Date().toISOString();
      }

      preferences.analytics = analytics;
      await userRepo.update(userId, {
        preferences: JSON.stringify(preferences)
      });
    }
  }

  // Mock implementations for metrics (would be real calculations in production)
  private async getActiveUsersCount(): Promise<number> {
    return 150;
  }
  private async getTotalUsersCount(): Promise<number> {
    return 1000;
  }
  private async getMessagesPerMinute(): Promise<number> {
    return 25;
  }
  private async getAverageResponseTime(): Promise<number> {
    return 120;
  }
  private async getErrorRate(): Promise<number> {
    return 0.02;
  }
  private async getAIRequestsPerHour(): Promise<number> {
    return 45;
  }
  private getMemoryUsage(): number {
    return 65.5;
  }
  private getCPUUsage(): number {
    return 23.8;
  }

  private getDefaultMetrics(): SystemMetrics {
    return {
      activeUsers: 0,
      totalUsers: 0,
      messagesPerMinute: 0,
      averageResponseTime: 0,
      errorRate: 0,
      aiRequestsPerHour: 0,
      memoryUsage: 0,
      cpuUsage: 0
    };
  }

  private async getTopErrors(): Promise<Array<{ error: string; count: number; lastOccurred: Date }>> {
    return [
      { error: "Rate limit exceeded", count: 45, lastOccurred: new Date() },
      { error: "AI service timeout", count: 23, lastOccurred: new Date() },
      { error: "Database connection failed", count: 12, lastOccurred: new Date() }
    ];
  }

  private async getUserGrowthData(): Promise<Array<{ date: string; newUsers: number; totalUsers: number }>> {
    const data = [];
    const now = new Date();

    for (let i = 6; i >= 0; i--) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      data.push({
        date: date.toISOString().split("T")[0],
        newUsers: Math.floor(Math.random() * 20) + 5,
        totalUsers: 1000 - (i * 10)
      });
    }

    return data;
  }
}
