import { Scenes, Composer, Markup } from "telegraf";
import { TBotContext } from "../core/types";
import { BotServices } from "../core/core";
import { WIZARD_MESSAGES, VALIDATION_MESSAGES, SUCCESS_MESSAGES, SCENES } from "../constants";

interface SearchData {
  type?: "tasks" | "birthdays" | "events" | "all";
  query?: string;
  filters?: {
    status?: string;
    priority?: string;
    dateRange?: string;
  };
  results?: any[];
}

interface WizardState {
  data: SearchData;
}

export class SearchWizard {
  private services: BotServices;

  constructor(services: BotServices) {
    this.services = services;
  }

  createScene(): Scenes.WizardScene<TBotContext> {
    const scene = new Scenes.WizardScene<TBotContext>(
      SCENES.SEARCH,
      this.stepSearchType.bind(this),
      this.stepSearchQuery.bind(this),
      this.stepResults.bind(this)
    );

    scene.command("cancel", this.handleCancel.bind(this));
    scene.action("cancel", this.handleCancel.bind(this));
    scene.action("back", this.handleBack.bind(this));
    scene.action(/^search_/, this.handleSearchTypeSelection.bind(this));

    return scene;
  }

  private async stepSearchType(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    ((ctx.wizard.state as WizardState) as WizardState).data = {};

    const message = `🔍 *Search Your Data*

What would you like to search for?

Choose from the options below:`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("📝 Tasks", "search_tasks"),
          Markup.button.callback("🎂 Birthdays", "search_birthdays")
        ],
        [
          Markup.button.callback("✝️ Events", "search_events"),
          Markup.button.callback("🔍 Everything", "search_all")
        ],
        [Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepSearchQuery(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    const wizardState = ctx.wizard.state as WizardState;

    if (!wizardState.data.type) {
      await ctx.reply("⚠️ Please select what to search for first.");
      return ctx.wizard.back();
    }

    const typeNames = {
      tasks: "tasks",
      birthdays: "birthdays",
      events: "events",
      all: "everything"
    } as const;

    const searchType = wizardState.data.type;
    const typeName = typeNames[searchType];
    const message = `🔍 *Search ${typeName.charAt(0).toUpperCase() + typeName.slice(1)}*

Enter your search query:

*Examples:*
• "meeting" (find tasks/events with "meeting")
• "John" (find birthdays for "John")
• "urgent" (find high priority items)`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepResults(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    if (ctx.message && "text" in ctx.message) {
      const query = ctx.message.text.trim();

      if (query.length < 2) {
        await ctx.reply("⚠️ Search query must be at least 2 characters long. Please try again:");
        return;
      }

      (ctx.wizard.state as WizardState).data.query = query;
    } else {
      await ctx.reply("⚠️ Please enter a text search query:");
      return;
    }

    const wizardState = ctx.wizard.state as WizardState;
    
    // Perform the search
    const results = await this.performSearch(ctx, wizardState);
    wizardState.data.results = results;

    if (results.length === 0) {
      await ctx.reply(`🔍 *No Results Found*

No ${wizardState.data.type} found matching "${wizardState.data.query}".

Try:
• Different keywords
• Broader search terms
• Check spelling`, {
        parse_mode: "Markdown",
        ...Markup.inlineKeyboard([
          [Markup.button.callback("🔍 New Search", "search_" + wizardState.data.type)],
          [Markup.button.callback("🏠 Main Menu", "main_menu")]
        ])
      });
    } else {
      await this.displayResults(ctx, wizardState);
    }

    return ctx.scene.leave();
  }

  private async performSearch(ctx: TBotContext, wizardState: WizardState): Promise<any[]> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return [];
    }

    const { type, query } = wizardState.data;
    const results: any[] = [];

    try {
      if (type === "tasks" || type === "all") {
        const tasks = await this.services.taskService.searchTasks(userId, query!);
        results.push(...tasks.map(task => ({ ...task, _type: "task" })));
      }

      if (type === "birthdays" || type === "all") {
        const birthdays = await this.services.birthdayService.searchBirthdays(userId, query!);
        results.push(...birthdays.map(birthday => ({ ...birthday, _type: "birthday" })));
      }

      if (type === "events" || type === "all") {
        const events = await this.services.eventService.searchEvents(userId, query!);
        results.push(...events.map(event => ({ ...event, _type: "event" })));
      }
    } catch (error) {
      console.error("Search error:", error);
    }

    return results;
  }

  private async displayResults(ctx: TBotContext, wizardState: WizardState): Promise<void> {
    const { type, query, results } = wizardState.data;
    const resultCount = results!.length;

    const displayResults = results!.slice(0, 10); // Show first 10 results

    let message = `🔍 *Search Results*\n\nFound ${resultCount} result${resultCount !== 1 ? 's' : ''} for "${query}"\n\n`;

    displayResults.forEach((result: any, index: number) => {
      const icon = result._type === "task" ? "📝" : result._type === "birthday" ? "🎂" : "✝️";
      const title = result.title || result.name || result.description || "Untitled";
      message += `${index + 1}. ${icon} ${title.substring(0, 40)}${title.length > 40 ? "..." : ""}\n`;
    });

    if (resultCount > 10) {
      message += `\n... and ${resultCount - 10} more results`;
    }

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("🔍 New Search", "search_" + type)],
        [Markup.button.callback("🏠 Main Menu", "main_menu")]
      ])
    });
  }

  private async handleSearchTypeSelection(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const wizardState = ctx.wizard.state as WizardState;
    const type = ctx.callbackQuery.data.replace("search_", "") as "tasks" | "birthdays" | "events" | "all";

    wizardState.data.type = type;
    await ctx.answerCbQuery();

    return ctx.wizard.next();
  }

  private async handleCancel(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    await ctx.answerCbQuery?.();
    await ctx.reply("❌ Search cancelled.");
    return ctx.scene.leave();
  }

  private async handleBack(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    await ctx.answerCbQuery?.();
    return ctx.wizard.back();
  }
}
