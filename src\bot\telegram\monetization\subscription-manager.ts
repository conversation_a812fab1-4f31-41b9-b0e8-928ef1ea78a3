// Subscription and monetization management system

import { TBotContext } from "../core/types";
import { RepositoryFactory } from "@/repositories";
import { Markup } from "telegraf";

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  currency: string;
  interval: "monthly" | "yearly";
  features: string[];
  quotas: {
    tasks: number;
    aiRequests: number;
    birthdays: number;
    notifications: number;
    exports: number;
  };
  popular?: boolean;
  trialDays?: number;
}

export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: "active" | "cancelled" | "expired" | "trial" | "pending";
  startDate: Date;
  endDate: Date;
  trialEndDate?: Date;
  paymentMethod?: string;
  paymentId?: string;
  cancelledAt?: Date;
  cancelReason?: string;
}

export interface PaymentIntent {
  id: string;
  userId: string;
  planId: string;
  amount: number;
  currency: string;
  status: "pending" | "succeeded" | "failed" | "cancelled";
  paymentUrl?: string;
  createdAt: Date;
  expiresAt: Date;
}

export class SubscriptionManager {
  private repositoryFactory: RepositoryFactory;
  private plans: Map<string, SubscriptionPlan>;

  constructor(repositoryFactory: RepositoryFactory) {
    this.repositoryFactory = repositoryFactory;
    this.plans = new Map();
    this.initializePlans();
  }

  /**
   * Initialize subscription plans
   */
  private initializePlans(): void {
    const plans: SubscriptionPlan[] = [
      {
        id: "premium_monthly",
        name: "Premium Monthly",
        price: 9.99,
        currency: "USD",
        interval: "monthly",
        trialDays: 7,
        features: [
          "unlimited_tasks",
          "unlimited_birthdays",
          "enhanced_ai",
          "advanced_notifications",
          "export_data",
          "task_templates",
          "bulk_operations",
          "priority_support",
          "custom_themes"
        ],
        quotas: {
          tasks: -1,
          aiRequests: 100,
          birthdays: -1,
          notifications: -1,
          exports: 10
        }
      },
      {
        id: "premium_yearly",
        name: "Premium Yearly",
        price: 99.99,
        currency: "USD",
        interval: "yearly",
        popular: true,
        trialDays: 14,
        features: [
          "unlimited_tasks",
          "unlimited_birthdays",
          "enhanced_ai",
          "advanced_notifications",
          "export_data",
          "task_templates",
          "bulk_operations",
          "priority_support",
          "custom_themes",
          "api_access"
        ],
        quotas: {
          tasks: -1,
          aiRequests: 200,
          birthdays: -1,
          notifications: -1,
          exports: 50
        }
      }
    ];

    plans.forEach(plan => this.plans.set(plan.id, plan));
  }

  /**
   * Show subscription plans to user
   */
  async showSubscriptionPlans(ctx: TBotContext): Promise<void> {
    const currentTier = ctx.session?.user?.tier.type;

    if (currentTier === "paid") {
      await this.showCurrentSubscription(ctx);
      return;
    }

    if (currentTier === "admin") {
      await ctx.reply("👑 You have Admin access with all features unlocked!");
      return;
    }

    const monthlyPlan = this.plans.get("premium_monthly")!;
    const yearlyPlan = this.plans.get("premium_yearly")!;

    const message = `
💎 *Choose Your Premium Plan*

**${monthlyPlan.name}** - $${monthlyPlan.price}/month
${monthlyPlan.trialDays} days free trial
• Unlimited tasks & birthdays
• ${monthlyPlan.quotas.aiRequests} AI requests/day
• Advanced features
• Priority support

**${yearlyPlan.name}** - $${yearlyPlan.price}/year 🔥
${yearlyPlan.trialDays} days free trial
• Everything in Monthly
• ${yearlyPlan.quotas.aiRequests} AI requests/day
• API access
• **Save $20/year!**

**Current Plan:** Free
• 10 tasks max
• 5 birthdays max
• 3 AI requests/day
• Basic features only

Choose your plan:
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("📅 Monthly ($9.99)", "subscribe_premium_monthly")],
        [Markup.button.callback("🔥 Yearly ($99.99)", "subscribe_premium_yearly")],
        [Markup.button.callback("❓ Compare Features", "compare_features")],
        [Markup.button.callback("💬 Contact Support", "contact_support")]
      ])
    });
  }

  /**
   * Handle subscription selection
   */
  async handleSubscriptionSelection(ctx: TBotContext, planId: string): Promise<void> {
    const plan = this.plans.get(planId);
    if (!plan) {
      await ctx.reply("❌ Invalid plan selected. Please try again.");
      return;
    }

    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    // Check if user already has an active subscription
    const existingSubscription = await this.getUserSubscription(userId);
    if (existingSubscription && existingSubscription.status === "active") {
      await ctx.reply("✅ You already have an active subscription! Use /subscription to manage it.");
      return;
    }

    // Create payment intent
    const paymentIntent = await this.createPaymentIntent(userId, planId);

    const message = `
💳 *Complete Your Subscription*

**Plan:** ${plan.name}
**Price:** $${plan.price}/${plan.interval}
**Trial:** ${plan.trialDays} days free

**What happens next:**
1. Click "Pay Now" to complete payment
2. Start your ${plan.trialDays}-day free trial
3. Get instant access to all Premium features
4. Cancel anytime during trial (no charge)

Your subscription will auto-renew after the trial period.
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.url("💳 Pay Now", paymentIntent.paymentUrl || "https://payment.example.com")],
        [Markup.button.callback("❌ Cancel", "cancel_subscription")],
        [Markup.button.callback("❓ Payment FAQ", "payment_faq")]
      ])
    });

    // Track business metric
    // await this.analytics.trackBusinessMetric(ctx, 'subscription_start_attempt', plan.price);
  }

  /**
   * Process successful payment
   */
  async processSuccessfulPayment(userId: string, planId: string, paymentId: string): Promise<void> {
    const plan = this.plans.get(planId);
    if (!plan) {
      throw new Error("Invalid plan");
    }

    const now = new Date();
    const trialEndDate = new Date(now.getTime() + (plan.trialDays || 0) * 24 * 60 * 60 * 1000);
    const endDate = new Date(trialEndDate);

    if (plan.interval === "monthly") {
      endDate.setMonth(endDate.getMonth() + 1);
    } else {
      endDate.setFullYear(endDate.getFullYear() + 1);
    }

    const subscription: Subscription = {
      id: this.generateId(),
      userId,
      planId,
      status: plan.trialDays ? "trial" : "active",
      startDate: now,
      endDate,
      trialEndDate: plan.trialDays ? trialEndDate : undefined,
      paymentId
    };

    // Save subscription to database
    await this.saveSubscription(subscription);

    // Update user tier
    await this.updateUserTier(userId, "paid", plan);

    // Send confirmation message
    // This would be called via webhook from payment processor
  }

  /**
   * Cancel subscription
   */
  async cancelSubscription(ctx: TBotContext, reason?: string): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    const subscription = await this.getUserSubscription(userId);
    if (!subscription || subscription.status !== "active") {
      await ctx.reply("❌ No active subscription found.");
      return;
    }

    // Update subscription status
    subscription.status = "cancelled";
    subscription.cancelledAt = new Date();
    subscription.cancelReason = reason;

    await this.saveSubscription(subscription);

    // Don't downgrade immediately - let them use premium until end of billing period
    const message = `
😢 *Subscription Cancelled*

Your Premium features will remain active until ${subscription.endDate.toLocaleDateString()}.

After that, you'll be moved to the Free plan:
• 10 tasks max
• 5 birthdays max  
• 3 AI requests/day
• Basic features only

**We're sorry to see you go!**
If you change your mind, you can resubscribe anytime.

Reason for cancellation: ${reason || "Not specified"}
    `;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("🔄 Reactivate", "reactivate_subscription")],
        [Markup.button.callback("💬 Feedback", "cancellation_feedback")]
      ])
    });

    // Track business metric
    // await this.analytics.trackBusinessMetric(ctx, 'subscription_cancel', 0, { reason });
  }

  /**
   * Show current subscription details
   */
  async showCurrentSubscription(ctx: TBotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    const subscription = await this.getUserSubscription(userId);
    if (!subscription) {
      await ctx.reply("❌ No subscription found.");
      return;
    }

    const plan = this.plans.get(subscription.planId);
    if (!plan) {
      await ctx.reply("❌ Subscription plan not found.");
      return;
    }

    const statusEmoji = {
      active: "✅",
      trial: "🆓",
      cancelled: "❌",
      expired: "⏰",
      pending: "⏳"
    };

    const message = `
💎 *Your Subscription*

**Status:** ${statusEmoji[subscription.status]} ${subscription.status.toUpperCase()}
**Plan:** ${plan.name}
**Price:** $${plan.price}/${plan.interval}

${subscription.trialEndDate && subscription.status === "trial" ?
    `**Trial ends:** ${subscription.trialEndDate.toLocaleDateString()}` :
    `**Next billing:** ${subscription.endDate.toLocaleDateString()}`
}

**Your Premium Features:**
• Unlimited tasks & birthdays
• ${plan.quotas.aiRequests} AI requests/day
• Advanced notifications
• Data export
• Task templates
• Priority support
• Custom themes

**Usage This Month:**
• AI Requests: ${ctx.session?.user?.tier.quotas.aiRequests.used || 0}/${plan.quotas.aiRequests}
• Exports: ${ctx.session?.user?.tier.quotas.exports.used || 0}/${plan.quotas.exports}
    `;

    const buttons = subscription.status === "active" ? [
      [Markup.button.callback("❌ Cancel Subscription", "cancel_subscription")],
      [Markup.button.callback("💳 Update Payment", "update_payment")],
      [Markup.button.callback("📧 Download Invoice", "download_invoice")]
    ] : subscription.status === "cancelled" ? [
      [Markup.button.callback("🔄 Reactivate", "reactivate_subscription")]
    ] : [
      [Markup.button.callback("💳 Complete Payment", "complete_payment")]
    ];

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard(buttons)
    });
  }

  /**
   * Handle upgrade prompts with smart timing
   */
  async showUpgradePrompt(
    ctx: TBotContext,
    trigger: "quota_exceeded" | "feature_locked" | "periodic",
    feature?: string
  ): Promise<void> {
    const user = ctx.session?.user;
    if (!user || user.tier.type !== "free") {
      return;
    }

    // Smart upgrade prompt timing
    const promptCount = user.tier.upgradePromptCount || 0;
    const lastPrompt = user.tier.lastUpgradePrompt;

    // Don't spam users with upgrade prompts
    if (lastPrompt && promptCount > 3) {
      const hoursSinceLastPrompt = (Date.now() - lastPrompt.getTime()) / (1000 * 60 * 60);
      if (hoursSinceLastPrompt < 24) {
        return;
      } // Wait 24 hours after 3 prompts
    }

    const messages = {
      quota_exceeded: `
🚀 *Upgrade to Premium*

You've reached your ${feature} limit! 

Premium users get:
• Unlimited ${feature}
• 100 AI requests/day
• Advanced features
• Priority support

Start your 7-day free trial now!
      `,
      feature_locked: `
💎 *Premium Feature*

${feature} is available for Premium users only.

Unlock all features with Premium:
• Unlimited everything
• Advanced AI capabilities
• Export & backup
• Priority support

Try Premium free for 7 days!
      `,
      periodic: `
✨ *Ready to Supercharge Your Productivity?*

You've been using Novers for ${this.getDaysActive(user)} days!

Upgrade to Premium and get:
• Unlimited tasks & birthdays
• 100 AI requests/day
• Advanced features
• Export capabilities

Special offer: 7 days free trial!
      `
    };

    await ctx.reply(messages[trigger], {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("🆓 Start Free Trial", "start_trial")],
        [Markup.button.callback("💎 View Plans", "view_plans")],
        [Markup.button.callback("❌ Maybe Later", "dismiss_upgrade")]
      ])
    });

    // Update prompt tracking
    user.tier.upgradePromptCount = promptCount + 1;
    user.tier.lastUpgradePrompt = new Date();

    // Track business metric
    // await this.analytics.trackBusinessMetric(ctx, 'upgrade_prompt', 0, { trigger, feature });
  }

  /**
   * Get user's subscription
   */
  private async getUserSubscription(userId: string): Promise<Subscription | null> {
    // Implementation would query database
    // For now, return mock data
    return null;
  }

  /**
   * Save subscription to database
   */
  private async saveSubscription(subscription: Subscription): Promise<void> {
    // Implementation would save to database
    console.log("Saving subscription:", subscription);
  }

  /**
   * Create payment intent
   */
  private async createPaymentIntent(userId: string, planId: string): Promise<PaymentIntent> {
    const plan = this.plans.get(planId);
    if (!plan) {
      throw new Error("Invalid plan");
    }

    const paymentIntent: PaymentIntent = {
      id: this.generateId(),
      userId,
      planId,
      amount: plan.price,
      currency: plan.currency,
      status: "pending",
      paymentUrl: `https://payment.example.com/pay/${this.generateId()}`,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
    };

    // Save to database
    console.log("Created payment intent:", paymentIntent);

    return paymentIntent;
  }

  /**
   * Update user tier after successful payment
   */
  private async updateUserTier(userId: string, tierType: "paid", plan: SubscriptionPlan): Promise<void> {
    // Implementation would update user tier in database
    console.log(`Updating user ${userId} to ${tierType} tier with plan ${plan.id}`);
  }

  /**
   * Get days since user registration
   */
  private getDaysActive(user: any): number {
    const now = new Date();
    const created = new Date(user.createdAt);
    return Math.floor((now.getTime() - created.getTime()) / (1000 * 60 * 60 * 24));
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get all available plans
   */
  getPlans(): SubscriptionPlan[] {
    return Array.from(this.plans.values());
  }

  /**
   * Get plan by ID
   */
  getPlan(planId: string): SubscriptionPlan | undefined {
    return this.plans.get(planId);
  }
}
