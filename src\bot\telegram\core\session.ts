// Production-scale session management with persistent storage

import { RepositoryFactory } from "../../../repositories";
import {
  TBotContext,
  TWizardSession,
  UserTier,
  UserQuotas,
  UserPreferences,
  UserAnalytics,
  RateLimitData,
  type TSession
} from "./types";
import { USER_QUOTAS, FEATURES, RATE_LIMITS } from "../constants/config";

export class SessionManager {
  private repositoryFactory: RepositoryFactory;

  constructor(repositoryFactory: RepositoryFactory) {
    this.repositoryFactory = repositoryFactory;
  }

  /**
 * Helper to get session data from context (simplified approach)
 */
  private getSessionData(ctx: TBotContext): TSession {
    return ctx.session;
  }

  /**
   * Helper to get scene session data from context (simplified approach)
   */
  private getSceneSessionData(ctx: TBotContext): TWizardSession {
    return ctx.scene.session;
  }

  /**
   * Helper to set session data in context (simplified approach)
   */
  private setSessionData(ctx: TBotContext, data: TSession): void {
    ctx.session = data;
  }

  /**
   * Helper to set scene session data in context (simplified approach)
   */
  private setSceneSessionData(ctx: TBotContext, data: TWizardSession): void {
    if (data.currentOperation) {
      ctx.scene.session.currentOperation = data.currentOperation;
    }
    if (data.tempData) {
      ctx.scene.session.tempData = data.tempData;
    }
  }

  /**
   * Initialize or load user session with persistent storage
   */
  async initializeSession(ctx: TBotContext): Promise<void> {
    const userId = ctx.from?.id.toString();
    if (!userId) {
      return;
    }

    // Load existing session from database or create new one
    let sessionData = await this.loadSessionFromStorage(userId);

    if (!sessionData) {
      sessionData = await this.createNewUserSession(ctx);
    } else {
      // Update last active time
      sessionData.user!.lastActive = new Date();
      await this.saveSessionToStorage(userId, sessionData);
    }

    // Merge with current session
    ctx.session = { ...ctx.session, ...sessionData };
  }

  /**
   * Create a new user session with default settings
   */
  private async createNewUserSession(ctx: TBotContext): Promise<TSession> {
    const userId = ctx.from!.id.toString();
    const username = ctx.from!.username;
    const firstName = ctx.from!.first_name;
    const lastName = ctx.from!.last_name;

    // Determine initial user tier (default to free)
    const userTier = await this.determineUserTier(userId);

    const sessionData: TSession = {
      user: {
        id: userId,
        username,
        firstName,
        lastName,
        tier: userTier,
        preferences: this.getDefaultPreferences(),
        analytics: this.getDefaultAnalytics(),
        createdAt: new Date(),
        lastActive: new Date()
      },
      rateLimit: this.getDefaultRateLimit()
    };

    // Save to database
    await this.saveUserToDatabase(sessionData.user);
    await this.saveSessionToStorage(userId, sessionData);

    return sessionData;
  }

  /**
   * Determine user tier based on database records or admin list
   */
  private async determineUserTier(userId: string): Promise<UserTier> {
    // Check if user is admin (you can maintain this list in environment variables)
    const adminIds = process.env.ADMIN_USER_IDS?.split(",") || [];
    if (adminIds.includes(userId)) {
      return this.createUserTier("admin");
    }

    // Check for existing subscription in database
    const userRepo = this.repositoryFactory.getUserRepository();
    const existingUser = await userRepo.findById(userId);

    if (existingUser?.preferences) {
      try {
        const prefs = typeof existingUser.preferences === "string"
          ? JSON.parse(existingUser.preferences)
          : existingUser.preferences;
        if (prefs.subscription?.active) {
          return this.createUserTier("paid");
        }
      } catch (error) {
        console.error("Error parsing user preferences:", error);
      }
    }

    return this.createUserTier("free");
  }

  /**
   * Create user tier with quotas and features
   */
  private createUserTier(type: UserTier["type"]): UserTier {
    const quotaLimits = USER_QUOTAS[type.toUpperCase() as keyof typeof USER_QUOTAS];
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const quotas: UserQuotas = {
      aiRequests: { used: 0, limit: quotaLimits.AI_REQUESTS, resetDate: tomorrow },
      tasks: { used: 0, limit: quotaLimits.TASKS },
      birthdays: { used: 0, limit: quotaLimits.BIRTHDAYS },
      notifications: { used: 0, limit: quotaLimits.NOTIFICATIONS },
      dailyActions: { used: 0, limit: quotaLimits.DAILY_ACTIONS, resetDate: tomorrow },
      exports: { used: 0, limit: quotaLimits.EXPORTS, resetDate: tomorrow }
    };

    return {
      type,
      features: FEATURES[type.toUpperCase() as keyof typeof FEATURES],
      quotas,
      upgradePromptCount: 0
    };
  }

  /**
   * Check if user has access to a specific feature
   */
  hasFeatureAccess(ctx: TBotContext, feature: string): boolean {
    const sessionData = this.getSessionData(ctx);
    return sessionData?.user?.tier.features.includes(feature) || false;
  }

  /**
   * Check and enforce quota limits
   */
  async checkQuota(ctx: TBotContext, quotaType: keyof UserQuotas): Promise<boolean> {
    const sessionData = this.getSessionData(ctx);
    const user = sessionData?.user;
    if (!user) {
      return false;
    }

    const quota = user.tier.quotas[quotaType];

    // Unlimited quota (admin)
    if (quota.limit === -1) {
      return true;
    }

    // Check if quota needs reset
    if ("resetDate" in quota && quota.resetDate < new Date()) {
      quota.used = 0;
      quota.resetDate = this.getNextResetDate();
      await this.saveSessionToStorage(user.id, ctx.session);
    }

    return quota.used < quota.limit;
  }

  /**
   * Consume quota for a specific action
   */
  async consumeQuota(ctx: TBotContext, quotaType: keyof UserQuotas, amount: number = 1): Promise<boolean> {
    const user = ctx.session?.user;
    if (!user) {
      return false;
    }

    const quota = user.tier.quotas[quotaType];

    // Unlimited quota (admin)
    if (quota.limit === -1) {
      return true;
    }

    if (quota.used + amount <= quota.limit) {
      quota.used += amount;
      await this.saveSessionToStorage(user.id, ctx.session);
      return true;
    }

    return false;
  }

  /**
   * Check rate limits
   */
  async checkRateLimit(ctx: TBotContext): Promise<boolean> {
    const rateLimit = ctx.session?.rateLimit;
    if (!rateLimit) {
      return true;
    }

    const now = new Date();

    // Check if user is currently blocked
    if (rateLimit.isBlocked && rateLimit.blockUntil && rateLimit.blockUntil > now) {
      return false;
    }

    // Reset block if expired
    if (rateLimit.isBlocked && rateLimit.blockUntil && rateLimit.blockUntil <= now) {
      rateLimit.isBlocked = false;
      rateLimit.blockUntil = undefined;
      rateLimit.requestCount = 0;
      rateLimit.windowStart = now;
    }

    // Check if we need to reset the window
    const windowDuration = 60 * 1000; // 1 minute
    if (now.getTime() - rateLimit.windowStart.getTime() > windowDuration) {
      rateLimit.requestCount = 0;
      rateLimit.windowStart = now;
    }

    // Check rate limit
    if (rateLimit.requestCount >= RATE_LIMITS.GENERAL.MAX_REQUESTS) {
      rateLimit.isBlocked = true;
      rateLimit.blockUntil = new Date(now.getTime() + RATE_LIMITS.GENERAL.BLOCK_DURATION_MS);
      await this.saveSessionToStorage(ctx.session?.user?.id || "", ctx.session);
      return false;
    }

    // Increment request count
    rateLimit.requestCount++;
    rateLimit.lastRequest = now;

    return true;
  }

  /**
   * Update user analytics
   */
  async updateAnalytics(ctx: TBotContext, action: string): Promise<void> {
    const user = ctx.session?.user;
    if (!user) {
      return;
    }

    user.analytics.totalCommands++;
    user.analytics.lastFeatureUsed = action;

    if (!user.analytics.featuresUsed.includes(action)) {
      user.analytics.featuresUsed.push(action);
    }

    await this.saveSessionToStorage(user.id, ctx.session);
  }

  /**
   * Load session from persistent storage (D1 database)
   */
  private async loadSessionFromStorage(userId: string): Promise<TSession | null> {
    try {
      const userRepo = this.repositoryFactory.getUserRepository();
      const user = await userRepo.findById(userId);

      if (!user) {
        return null;
      }

      // For now, create default session data since we don't have extended storage yet
      // TODO: Load extended session data from separate storage mechanism cloudflare KV

      return {
        // Production session properties
        user: {
          id: userId,
          username: user.username || undefined,
          firstName: user.firstName,
          lastName: user.lastName || undefined,
          tier: this.createUserTier("free"), // Default to free, will be upgraded based on actual subscription
          preferences: this.getDefaultPreferences(),
          analytics: this.getDefaultAnalytics(),
          createdAt: new Date(user.createdAt),
          lastActive: new Date(user.lastActive)
        },
        rateLimit: this.getDefaultRateLimit()
      };
    } catch (error) {
      console.error("Failed to load session from storage:", error);
      return null;
    }
  }

  /**
   * Save session to persistent storage
   */
  private async saveSessionToStorage(userId: string, sessionData: TSession): Promise<void> {
    try {
      const userRepo = this.repositoryFactory.getUserRepository();

      // Update basic user preferences (compatible with existing schema)
      const basicPreferences = {
        timezone: sessionData.user?.preferences?.timezone || "UTC",
        notificationTime: sessionData.user?.preferences?.notificationTime || "09:00",
        mode: "chat" as const
      };

      // Store extended session data in a separate field (we'll need to add this to the schema)
      // For now, we'll store it as JSON in a custom field or use a separate table

      await userRepo.updateLastActive(userId);
      await userRepo.updatePreferences(userId, basicPreferences);

      // TODO: Store extended session data (tier, analytics, rateLimit) in a separate mechanism
      // This could be a separate session table or a JSON field in the user table

    } catch (error) {
      console.error("Failed to save session to storage:", error);
    }
  }

  /**
   * Save user to database
   */
  private async saveUserToDatabase(user: TSession["user"]): Promise<void> {
    if (!user) {
      return;
    }

    try {
      const userRepo = this.repositoryFactory.getUserRepository();

      // Create basic user preferences compatible with schema
      const basicPreferences = {
        timezone: user.preferences?.timezone || "UTC",
        notificationTime: user.preferences?.notificationTime || "09:00",
        mode: "chat" as const
      };

      await userRepo.create({
        id: user.id,
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        createdAt: user.createdAt.toISOString(),
        lastActive: user.lastActive.toISOString(),
        preferences: basicPreferences
      });

      // TODO: Store extended user data (tier, analytics) in a separate mechanism

    } catch (error) {
      console.error("Failed to save user to database:", error);
    }
  }

  private getDefaultPreferences(): UserPreferences {
    return {
      timezone: "UTC",
      language: "en",
      notificationTime: "09:00",
      quietHours: { start: "22:00", end: "08:00" },
      aiEnhancement: true,
      theme: "light",
      compactMode: false,
      soundEnabled: true
    };
  }

  private getDefaultAnalytics(): UserAnalytics {
    return {
      totalCommands: 0,
      totalMessages: 0,
      featuresUsed: [],
      sessionDuration: 0,
      averageSessionDuration: 0
    };
  }

  private getDefaultRateLimit(): RateLimitData {
    return {
      lastRequest: new Date(),
      requestCount: 0,
      windowStart: new Date(),
      isBlocked: false,
      warningCount: 0
    };
  }

  private getNextResetDate(): Date {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }
}
