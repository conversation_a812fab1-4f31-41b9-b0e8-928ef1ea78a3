import { Scenes, Composer, Markup } from "telegraf";
import { TBotContext } from "../core/types";
import { BotServices } from "../core/core";
import { WIZARD_MESSAGES, VALIDATION_MESSAGES, SUCCESS_MESSAGES, SCENES } from "../constants";

interface SettingsData {
  category?: "language" | "timezone" | "notifications" | "ai" | "theme";
  value?: string;
  originalSettings?: any;
}

interface WizardState {
  data: SettingsData;
}

export class SettingsWizard {
  private services: BotServices;

  constructor(services: BotServices) {
    this.services = services;
  }

  createScene(): Scenes.WizardScene<TBotContext> {
    const scene = new Scenes.WizardScene<TBotContext>(
      SCENES.SETTINGS,
      this.stepCategory.bind(this),
      this.stepValue.bind(this),
      this.stepConfirmation.bind(this)
    );

    scene.command("cancel", this.handleCancel.bind(this));
    scene.action("cancel", this.handleCancel.bind(this));
    scene.action("back", this.handleBack.bind(this));
    scene.action(/^setting_/, this.handleSettingSelection.bind(this));

    return scene;
  }

  private async stepCategory(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    const user = ctx.session?.user;
    if (!user) {
      return ctx.scene.leave();
    }

    ((ctx.wizard.state as WizardState) as WizardState).data = {
      originalSettings: { ...user.preferences }
    };

    const message = `⚙️ *Settings Configuration*

**Current Settings:**
🌍 Language: ${user.preferences.language}
🕐 Timezone: ${user.preferences.timezone}
🔔 Notifications: ${user.preferences.notificationTime}
🤖 AI Enhancement: ${user.preferences.aiEnhancement ? "Enabled" : "Disabled"}
🎨 Theme: ${user.preferences.theme}

What would you like to configure?`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("🌍 Language", "setting_language"),
          Markup.button.callback("🕐 Timezone", "setting_timezone")
        ],
        [
          Markup.button.callback("🔔 Notifications", "setting_notifications"),
          Markup.button.callback("🤖 AI Settings", "setting_ai")
        ],
        [
          Markup.button.callback("🎨 Theme", "setting_theme"),
          Markup.button.callback("🔇 Quiet Hours", "setting_quiet")
        ],
        [Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepValue(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    const wizardState = ctx.wizard.state as WizardState;
    const category = wizardState.data.category;

    if (!category) {
      await ctx.reply("⚠️ Please select a setting category first.");
      return ctx.wizard.back();
    }

    switch (category) {
      case "language":
        await this.showLanguageOptions(ctx);
        break;
      case "timezone":
        await this.showTimezoneOptions(ctx);
        break;
      case "notifications":
        await this.showNotificationOptions(ctx);
        break;
      case "ai":
        await this.showAIOptions(ctx);
        break;
      case "theme":
        await this.showThemeOptions(ctx);
        break;
    }

    return ctx.wizard.next();
  }

  private async stepConfirmation(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    const wizardState = ctx.wizard.state as WizardState;

    if (ctx.callbackQuery && "data" in ctx.callbackQuery) {
      const data = ctx.callbackQuery.data;

      if (data.startsWith("value_")) {
        wizardState.data.value = data.replace("value_", "");
        await ctx.answerCbQuery();
      } else if (data === "confirm_save") {
        await this.saveSettings(ctx, wizardState);
        return;
      }
    } else if (ctx.message && "text" in ctx.message) {
      wizardState.data.value = ctx.message.text.trim();
    }

    if (!wizardState.data.value) {
      await ctx.reply("⚠️ Please select or enter a value.");
      return;
    }

    const categoryNames = {
      language: "Language",
      timezone: "Timezone",
      notifications: "Notification Time",
      ai: "AI Enhancement",
      theme: "Theme"
    };

    const message = `✅ *Confirm Settings Change*

**Category:** ${categoryNames[wizardState.settingsData.category!]}
**New Value:** ${wizardState.settingsData.value}

Save this change?`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("✅ Save", "confirm_save")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });
  }

  private async showLanguageOptions(ctx: TBotContext): Promise<void> {
    const message = `🌍 *Select Language*

Choose your preferred language:`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("🇺🇸 English", "value_en"),
          Markup.button.callback("🇪🇸 Español", "value_es")
        ],
        [
          Markup.button.callback("🇫🇷 Français", "value_fr"),
          Markup.button.callback("🇩🇪 Deutsch", "value_de")
        ],
        [
          Markup.button.callback("🇮🇹 Italiano", "value_it"),
          Markup.button.callback("🇵🇹 Português", "value_pt")
        ],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });
  }

  private async showTimezoneOptions(ctx: TBotContext): Promise<void> {
    const message = `🕐 *Select Timezone*

Choose your timezone or enter a custom one:

*Common timezones:*`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("🇺🇸 EST (UTC-5)", "value_America/New_York"),
          Markup.button.callback("🇺🇸 PST (UTC-8)", "value_America/Los_Angeles")
        ],
        [
          Markup.button.callback("🇬🇧 GMT (UTC+0)", "value_Europe/London"),
          Markup.button.callback("🇩🇪 CET (UTC+1)", "value_Europe/Berlin")
        ],
        [
          Markup.button.callback("🇯🇵 JST (UTC+9)", "value_Asia/Tokyo"),
          Markup.button.callback("🇦🇺 AEST (UTC+10)", "value_Australia/Sydney")
        ],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    await ctx.reply("Or type your timezone (e.g., \"America/Chicago\", \"Europe/Paris\"):");
  }

  private async showNotificationOptions(ctx: TBotContext): Promise<void> {
    const message = `🔔 *Notification Settings*

When would you like to receive daily notifications?`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("🌅 08:00", "value_08:00"),
          Markup.button.callback("🌄 09:00", "value_09:00"),
          Markup.button.callback("🌞 10:00", "value_10:00")
        ],
        [
          Markup.button.callback("🕐 12:00", "value_12:00"),
          Markup.button.callback("🌆 18:00", "value_18:00"),
          Markup.button.callback("🌃 20:00", "value_20:00")
        ],
        [Markup.button.callback("🔇 Disable", "value_disabled")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    await ctx.reply("Or type a custom time (e.g., \"07:30\", \"15:45\"):");
  }

  private async showAIOptions(ctx: TBotContext): Promise<void> {
    const user = ctx.session?.user;
    const hasAI = this.services.sessionManager.hasFeatureAccess(ctx, "basic_ai");

    if (!hasAI) {
      await ctx.reply("🤖 AI features are not available on your current plan. Upgrade to Premium to access AI enhancement!", {
        reply_markup: {
          inline_keyboard: [
            [Markup.button.callback("💎 Upgrade", "upgrade")],
            [Markup.button.callback("⬅️ Back", "back")]
          ]
        }
      });
      return;
    }

    const message = `🤖 *AI Enhancement Settings*

Current: ${user?.preferences.aiEnhancement ? "Enabled" : "Disabled"}

AI enhancement helps improve your tasks with:
• Better descriptions
• Priority suggestions
• Due date recommendations
• Category suggestions`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("✅ Enable AI", "value_true"),
          Markup.button.callback("❌ Disable AI", "value_false")
        ],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });
  }

  private async showThemeOptions(ctx: TBotContext): Promise<void> {
    const hasCustomThemes = this.services.sessionManager.hasFeatureAccess(ctx, "custom_themes");

    const message = `🎨 *Theme Settings*

Choose your preferred theme:`;

    const buttons = [
      [
        Markup.button.callback("☀️ Light", "value_light"),
        Markup.button.callback("🌙 Dark", "value_dark")
      ]
    ];

    if (hasCustomThemes) {
      buttons.push([
        Markup.button.callback("🌈 Colorful", "value_colorful"),
        Markup.button.callback("🎯 Minimal", "value_minimal")
      ]);
    } else {
      buttons.push([Markup.button.callback("💎 More themes (Premium)", "upgrade")]);
    }

    buttons.push([Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]);

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard(buttons)
    });
  }

  private async handleSettingSelection(ctx: TBotContext): Promise<void> {
    if (!ctx.callbackQuery || !("data" in ctx.callbackQuery)) {
      return;
    }

    const wizardState = ctx.wizard.state as SettingsWizardSession;
    const category = ctx.callbackQuery.data.replace("setting_", "") as any;

    wizardState.settingsData.category = category;
    await ctx.answerCbQuery();
    return ctx.wizard.next();
  }

  private async saveSettings(ctx: TBotContext, wizardState: SettingsWizardSession): Promise<void> {
    try {
      await ctx.answerCbQuery();

      const user = ctx.session?.user;
      if (!user) {
        return ctx.scene.leave();
      }

      const { category, value } = wizardState.settingsData;

      // Update user preferences
      switch (category) {
        case "language":
          user.preferences.language = value!;
          break;
        case "timezone":
          user.preferences.timezone = value!;
          break;
        case "notifications":
          user.preferences.notificationTime = value!;
          break;
        case "ai":
          user.preferences.aiEnhancement = value === "true";
          break;
        case "theme":
          user.preferences.theme = value as "light" | "dark";
          break;
      }

      // Save to database (this would be implemented in the session manager)
      await this.services.sessionManager.updateAnalytics(ctx, "settings_updated");

      const categoryNames = {
        language: "Language",
        timezone: "Timezone",
        notifications: "Notification Time",
        ai: "AI Enhancement",
        theme: "Theme"
      };

      const successMessage = `${SUCCESS_MESSAGES.SETTINGS_UPDATED}

**${categoryNames[category!]}** has been updated to: ${value}

Your new settings are now active!`;

      await ctx.reply(successMessage, { parse_mode: "Markdown" });

      return ctx.scene.leave();
    } catch (error: any) {
      console.error("Error saving settings:", error);
      await ctx.reply("❌ Failed to save settings. Please try again.");
      return ctx.scene.leave();
    }
  }

  private async handleCancel(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    await ctx.reply("❌ Settings configuration cancelled.");
    return ctx.scene.leave();
  }

  private async handleBack(ctx: TBotContext): Promise<void> {
    await ctx.answerCbQuery?.();
    return ctx.wizard.back();
  }
}
