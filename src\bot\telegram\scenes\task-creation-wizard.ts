import { Scenes, Composer, Markup } from "telegraf";
import { TBotContext } from "../core/types";
import { BotServices } from "../core/core";
import { WIZARD_MESSAGES, VALIDATION_MESSAGES, SUCCESS_MESSAGES, SCENES } from "../constants";

interface TaskData {
  title?: string;
  description?: string;
  priority?: "low" | "medium" | "high";
}

interface WizardState {
  data: TaskData;
}

export class TaskCreationWizard {
  private services: BotServices;

  constructor(services: BotServices) {
    this.services = services;
  }

  createScene(): Scenes.WizardScene<TBotContext> {
    const scene = new Scenes.WizardScene<TBotContext>(
      SCENES.TASK_CREATION,
      this.stepTitle.bind(this),
      this.stepDescription.bind(this),
      this.stepPriority.bind(this),
      this.stepConfirmation.bind(this)
    );

    scene.command("cancel", this.handleCancel.bind(this));
    scene.action("cancel", this.handleCancel.bind(this));
    scene.action("back", this.handleBack.bind(this));
    scene.action("skip_description", this.skipDescription.bind(this));

    return scene;
  }

  private async stepTitle(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    const canCreateTask = await this.services.sessionManager.checkQuota(ctx, "tasks");
    if (!canCreateTask) {
      await ctx.reply("📊 You've reached your task limit. Upgrade to create more tasks!");
      return ctx.scene.leave();
    }

    ((ctx.wizard.state as WizardState) as WizardState).data = {};

    await ctx.reply(WIZARD_MESSAGES.TASK_CREATION.TITLE_PROMPT, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepDescription(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    if (ctx.message && "text" in ctx.message) {
      const title = ctx.message.text.trim();

      if (!title.length) {
        await ctx.reply(VALIDATION_MESSAGES.TASK_TITLE_REQUIRED);
        return;
      }
      if (title.length < 3) {
        await ctx.reply("❌ Task title must be at least 3 characters long. Please try again:");
        return;
      }
      if (title.length > 100) {
        await ctx.reply(VALIDATION_MESSAGES.TASK_TITLE_TOO_LONG);
        return;
      }

      (ctx.wizard.state as WizardState).data.title = title;
    } else {
      await ctx.reply("❌ Please enter a text message for the task title:");
      return;
    }

    await ctx.reply(WIZARD_MESSAGES.TASK_CREATION.DESCRIPTION_PROMPT, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("⏭️ Skip Description", "skip_description")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepPriority(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    if (ctx.message && "text" in ctx.message) {
      const description = ctx.message.text.trim();

      if (description.length > 500) {
        await ctx.reply("❌ Description is too long (max 500 characters). Please shorten it:");
        return;
      }

      (ctx.wizard.state as WizardState).data.description = description;
    }

    await ctx.reply(WIZARD_MESSAGES.TASK_CREATION.PRIORITY_PROMPT, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [
          Markup.button.callback("🔴 High", "priority_high"),
          Markup.button.callback("🟡 Medium", "priority_medium"),
          Markup.button.callback("🟢 Low", "priority_low")
        ],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    return ctx.wizard.next();
  }

  private async stepConfirmation(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    if (ctx.callbackQuery && "data" in ctx.callbackQuery) {
      const data = ctx.callbackQuery.data;

      if (data === "priority_high") {
        (ctx.wizard.state as WizardState).data.priority = "high";
      } else if (data === "priority_medium") {
        (ctx.wizard.state as WizardState).data.priority = "medium";
      } else if (data === "priority_low") {
        (ctx.wizard.state as WizardState).data.priority = "low";
      }

      await ctx.answerCbQuery();
    }

    // Set default priority if none selected
    if (!(ctx.wizard.state as WizardState).data.priority) {
      (ctx.wizard.state as WizardState).data.priority = "medium";
    }

    const taskData = (ctx.wizard.state as WizardState).data;
    const priorityEmoji = {
      high: "🔴",
      medium: "🟡",
      low: "🟢"
    };

    const message = `${WIZARD_MESSAGES.TASK_CREATION.CONFIRMATION_PROMPT}

**Title:** ${taskData.title}
**Description:** ${taskData.description || "None"}
**Priority:** ${priorityEmoji[taskData.priority!]} ${taskData.priority}`;

    await ctx.reply(message, {
      parse_mode: "Markdown",
      ...Markup.inlineKeyboard([
        [Markup.button.callback("✅ Create Task", "confirm_create")],
        [Markup.button.callback("⬅️ Back", "back"), Markup.button.callback("❌ Cancel", "cancel")]
      ])
    });

    if (ctx.callbackQuery && "data" in ctx.callbackQuery && ctx.callbackQuery.data === "confirm_create") {
      await this.createTask(ctx);
    }
  }

  private async createTask(ctx: TBotContext): Promise<void> {
    try {
      await ctx.answerCbQuery();

      await this.services.sessionManager.consumeQuota(ctx, "tasks");

      const data = (ctx.wizard.state as WizardState).data;
      const task = await this.services.taskService.createTask(
        ctx.from!.id.toString(),
        data.title!,
        data.description,
        data.priority || "medium"
      );

      const successMessage = `${SUCCESS_MESSAGES.TASK_CREATED}

**${task.title}**
${task.description ? `Description: ${task.description}` : ""}
Priority: ${data.priority || "medium"}
Status: Pending

Use /tasks to view all your tasks.`;

      await ctx.reply(successMessage, { parse_mode: "Markdown" });

      await this.services.sessionManager.updateAnalytics(ctx, "task_created");

      return ctx.scene.leave();
    } catch (error: any) {
      console.error("Error creating task:", error);
      await ctx.reply("❌ Failed to create task. Please try again.");
      return ctx.scene.leave();
    }
  }

  private async handleCancel(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    await ctx.answerCbQuery?.();
    await ctx.reply("❌ Task creation cancelled.");
    return ctx.scene.leave();
  }

  private async handleBack(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    await ctx.answerCbQuery?.();
    return ctx.wizard.back();
  }

  private async skipDescription(ctx: TBotContext): Promise<Scenes.WizardContextWizard<TBotContext> | void> {
    await ctx.answerCbQuery();
    return ctx.wizard.next();
  }
}
